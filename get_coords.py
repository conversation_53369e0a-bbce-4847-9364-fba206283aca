# region_picker.py
import pyautogui, time, msvcrt

print("移动到【左上角】按 S，移动到【右下角】再按 S。Ctrl-C 退出。")
p1 = p2 = None
try:
    while True:
        x, y = pyautogui.position()
        print(f"X:{x:4d} Y:{y:4d}", end="\r")
        if msvcrt.kbhit():
            key = msvcrt.getch()
            if key in (b's', b'S'):
                if p1 is None:
                    p1 = (x, y)
                    print(f"\n已记录左上角: {p1}")
                    time.sleep(0.3)
                else:
                    p2 = (x, y)
                    print(f"已记录右下角: {p2}")
                    x1, y1 = p1; x2, y2 = p2
                    region = (x1, y1, x2 - x1, y2 - y1)
                    print(f"\nregion = {region}")
                    with open("regions.txt", "a", encoding="utf-8") as f:
                        f.write(f"{region}\n")
                    p1 = p2 = None
                    print("\n继续：再选新的区域（左上 S → 右下 S）...")
        time.sleep(0.05)
except KeyboardInterrupt:
    print("\n已退出。")
