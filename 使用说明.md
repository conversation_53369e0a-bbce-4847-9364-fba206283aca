# 三角洲抢砖皮脚本使用说明

本文档提供了关于三角洲行动曼德尔砖皮抢购脚本的详细使用指南，帮助您正确配置和使用该脚本以提高抢购成功率。

## 一、准备工作

### 1. 环境要求

- **操作系统**：Windows 7/8/10/11
- **Python版本**：Python 3.6或更高版本
- **硬件要求**：
  - CPU：建议Intel Core i5或同等性能处理器
  - 内存：至少4GB RAM
  - GPU（可选）：支持CUDA的NVIDIA显卡，用于加速图像处理

### 2. 安装必要软件

#### 2.1 安装Python

1. 访问[Python官网](https://www.python.org/downloads/)下载最新版本的Python安装程序
2. 运行安装程序，勾选"Add Python to PATH"选项，然后点击"Install Now"
3. 安装完成后，打开命令提示符，输入`python --version`验证安装是否成功

#### 2.2 安装Tesseract OCR

1. 从[GitHub UB-Mannheim仓库](https://github.com/UB-Mannheim/tesseract/wiki)下载Tesseract OCR安装程序
2. 运行安装程序，记录安装路径（例如：`C:\Program Files\Tesseract-OCR`）
3. 确保安装时包含中文语言包

## 二、安装项目依赖

1. 下载本项目所有文件到您的电脑上
2. 打开命令提示符，切换到项目所在目录
3. 执行以下命令安装所需的Python库：

```bash
pip install pyautogui opencv-python numpy pytesseract pillow
```

如果安装过程中出现问题，可以尝试使用以下命令：

```bash
pip install --upgrade pip
pip install pyautogui opencv-python numpy pytesseract pillow --user
```

## 三、配置脚本

在运行脚本前，您需要根据自己的屏幕分辨率和游戏界面配置`auto_buy.py`文件中的参数。

### 1. 获取屏幕坐标

使用`get_coords.py`工具获取所需的屏幕坐标：

```bash
python get_coords.py
```

运行后，将鼠标移动到相应位置，控制台会显示当前鼠标坐标。需要获取的位置包括：

- 倒计时区域的左上角坐标和区域大小
- 购买按钮区域的左上角坐标和区域大小
- 确认按钮区域的左上角坐标和区域大小
- 确认购买按钮的精确坐标

### 2. 编辑配置文件

打开`auto_buy.py`文件，找到"用户配置区域"部分，修改以下参数：

```python
# --- 请在这里配置你的搜索区域 (x, y, 宽度, 高度) ---
TIMER_REGION = (2057, 1033, 246, 44)  # 倒计时区域
BUY_BUTTON_REGION = (1790, 937, 710, 323)  # 购买按钮区域
CONFIRM_BUTTON_REGION = (1403, 876, 602, 209)  # 确认按钮区域

# --- 性能配置 ---
CLICK_INTERVAL = 0.32  # 高速点击的间隔时间
CONFIRM_CLICKS = 20  # 确认点击次数
CONFIRM_CLICK_INTERVAL = 0.32  # 确认按钮的高速点击间隔时间

# --- 新增配置 ---
CONFIRM_BUTTON_FIXED_POS = (1583, 975)  # 确认购买按钮的固定坐标
PRE_CLICK_TIME = 1.0  # 倒计时结束前多久开始点击（秒）
POST_CLICK_TIME = 0.05  # 倒计时结束后持续点击多久（秒）
PYTESSERACT_PATH = r'C:\Software\TesseractOCR\tesseract.exe'  # Tesseract OCR安装路径

# --- GPU加速配置 ---
USE_GPU = True  # 设置为False可禁用GPU加速
GPU_DEVICE_ID = 0  # 如果有多个GPU，可以选择使用哪一个
```

**配置参数说明**：

- `TIMER_REGION`：倒计时显示区域，格式为(x坐标, y坐标, 宽度, 高度)
- `BUY_BUTTON_REGION`：购买按钮显示区域
- `CONFIRM_BUTTON_REGION`：确认购买按钮显示区域
- `CLICK_INTERVAL`：点击间隔时间（秒），值越小点击速度越快
- `CONFIRM_CLICKS`：确认购买按钮的点击次数
- `CONFIRM_CLICK_INTERVAL`：确认购买按钮的点击间隔时间
- `CONFIRM_BUTTON_FIXED_POS`：确认购买按钮的精确坐标，格式为(x, y)
- `PRE_CLICK_TIME`：倒计时结束前提前开始点击的时间
- `POST_CLICK_TIME`：倒计时结束后继续点击的时间
- `PYTESSERACT_PATH`：Tesseract OCR的安装路径
- `USE_GPU`：是否启用GPU加速
- `GPU_DEVICE_ID`：使用的GPU设备ID（多GPU环境下）

## 四、运行脚本

### 1. 启动游戏并进入购买界面

1. 启动三角洲行动游戏
2. 进入商店界面，找到曼德尔砖皮的购买页面
3. 确保倒计时显示在屏幕上

### 2. 运行抢购脚本

1. 打开命令提示符，切换到项目所在目录
2. 执行以下命令运行脚本：

```bash
python auto_buy.py
```

3. 脚本启动后，会显示初始化信息，并开始监测倒计时
4. 将鼠标保持在游戏窗口内，不要移动或点击，让脚本自动执行操作

### 3. 脚本执行流程

1. 脚本首先会初始化环境，检查GPU是否可用
2. 然后开始监测配置的倒计时区域
3. 当识别到倒计时接近结束时（根据`PRE_CLICK_TIME`设置），开始高频点击购买按钮
4. 在倒计时结束后，继续点击一段时间（根据`POST_CLICK_TIME`设置）
5. 检测到需要确认购买时，点击固定坐标的确认按钮多次
6. 整个过程完成后，脚本会自动退出

## 五、紧急停止方法

如果需要紧急停止脚本，可以使用以下方法：

- 将鼠标快速移动到屏幕的左上角，触发`pyautogui`的安全机制
- 按下`Ctrl+C`组合键中断脚本运行
- 直接关闭命令提示符窗口

## 六、常见问题及解决方案

### 1. 脚本无法识别倒计时

- **问题**：脚本无法正确识别倒计时时间
- **解决方案**：
  - 检查`TIMER_REGION`配置是否正确
  - 确保Tesseract OCR已正确安装并配置路径
  - 尝试调整游戏窗口的分辨率或缩放比例

### 2. 点击位置不准确

- **问题**：脚本点击的位置与实际按钮位置不符
- **解决方案**：
  - 使用`get_coords.py`重新获取准确的坐标
  - 确保游戏窗口没有被缩放或最小化
  - 检查屏幕分辨率是否与配置时相同

### 3. GPU加速无法启用

- **问题**：脚本无法启用GPU加速
- **解决方案**：
  - 确认您的NVIDIA显卡支持CUDA
  - 安装最新的NVIDIA驱动程序
  - 将`USE_GPU`设置为`False`，使用CPU模式

### 4. 抢购成功率低

- **问题**：多次运行脚本但抢购成功率不高
- **解决方案**：
  - 调整`PRE_CLICK_TIME`和`POST_CLICK_TIME`参数
  - 优化`CLICK_INTERVAL`和`CONFIRM_CLICK_INTERVAL`参数
  - 确保网络连接稳定，延迟低
  - 尝试在不同时间段运行脚本

## 七、高级使用技巧

### 1. 多账号抢购

可以为不同账号创建多个配置文件，然后依次运行脚本进行多账号抢购。

### 2. 定时启动

结合Windows的任务计划程序，可以设置脚本在特定时间自动启动，无需手动操作。

### 3. 调整识别灵敏度

如果倒计时识别不够稳定，可以尝试修改`extract_timer_value`函数中的图像处理参数，如对比度、二值化阈值等。

### 4. 监控日志分析

脚本运行过程中会输出详细的日志信息，可以根据这些信息分析问题所在并进行针对性优化。

## 八、注意事项

- 请合理使用本脚本，遵守游戏的用户协议和相关规定
- 不要过度依赖脚本，游戏的乐趣在于参与过程
- 定期检查和更新脚本，以适应游戏可能的更新和变化
- 注意保护个人账号安全，避免账号被封禁的风险
- 本脚本仅供学习和研究使用，请勿用于商业用途

祝您抢购成功！

---
更新时间：2023年12月