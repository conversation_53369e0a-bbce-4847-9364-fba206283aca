import pyautogui
import time
import sys
import re
import cv2
import numpy as np
import pytesseract
from PIL import Image

# --- 全局设置 ---
pyautogui.FAILSAFE = True

# ==============================================================================
# ▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼ 用户配置区域 ▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
# ==============================================================================
# --- 图片文件路径 ---
BUY_BUTTON_IMG = 'buy.png'
CONFIRM_BUTTON_IMG = 'yes.png'
TIMER_SIGNAL_IMG = 'timer.png'

# --- 请在这里配置你的搜索区域 (x, y, 宽度, 高度) ---
#手机
# TIMER_REGION = (2057, 1033, 246, 44)
# BUY_BUTTON_REGION = (1790, 937, 710, 323)
# CONFIRM_BUTTON_REGION = (1403, 876, 602, 209)
#电脑
TIMER_REGION = (1544, 859, 180, 26)
BUY_BUTTON_REGION = (1478, 884, 310, 68)
CONFIRM_BUTTON_REGION = (1065, 663, 306, 66)

# --- 性能配置 ---
CLICK_INTERVAL = 0.01  # 高速点击的间隔时间
CONFIRM_CLICKS = 20
CONFIRM_CLICK_INTERVAL = 0.01 # 确认按钮的高速点击间隔时间

# --- 新增配置 ---
# 确认购买按钮的固定坐标
# 手机
# CONFIRM_BUTTON_FIXED_POS = (1583, 975)
# 电脑
CONFIRM_BUTTON_FIXED_POS = (1225, 698)
# 倒计时结束前多久开始点击（秒）
PRE_CLICK_TIME = 1.0
# 倒计时结束后持续点击多久（秒）
POST_CLICK_TIME = 0.05
# OCR识别配置（如果使用pytesseract）
PYTESSERACT_PATH = r'C:\Program Files\Tesseract-OCR\tesseract.exe'  # 根据实际安装路径修改
# GPU加速配置
USE_GPU = False  # 设置为False可禁用GPU加速
GPU_DEVICE_ID = 0  # 如果有多个GPU，可以选择使用哪一个

# ==============================================================================
# ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲ 配置结束 ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲
# ==============================================================================

# 设置pytesseract路径（如果已安装）
try:
    pytesseract.pytesseract.tesseract_cmd = PYTESSERACT_PATH
except Exception:
    print("警告：未找到Tesseract OCR，将使用备用方法识别倒计时")

# 初始化GPU加速
if USE_GPU:
    try:
        # 检查CUDA是否可用
        cuda_available = cv2.cuda.getCudaEnabledDeviceCount() > 0
        if cuda_available:
            print(f"检测到 {cv2.cuda.getCudaEnabledDeviceCount()} 个CUDA设备，启用GPU加速")
            cv2.cuda.setDevice(GPU_DEVICE_ID)
            print(f"已选择GPU设备 {GPU_DEVICE_ID}")
        else:
            print("未检测到CUDA设备，将使用CPU模式")
            USE_GPU = False
    except Exception as e:
        print(f"初始化GPU失败: {e}，将使用CPU模式")
        USE_GPU = False

def extract_timer_value(region):
    """
    从指定区域截图并识别倒计时数值
    返回剩余秒数（浮点数）
    适配"XX分XX秒后解锁购买"格式
    支持GPU加速处理
    """
    try:
        # 截取倒计时区域的图像
        screenshot = pyautogui.screenshot(region=region)
        
        # 尝试使用OCR识别倒计时
        try:
            # 转换为OpenCV格式进行预处理
            img = np.array(screenshot)
            
            # GPU加速图像处理
            if USE_GPU:
                try:
                    # 将图像上传到GPU
                    gpu_img = cv2.cuda_GpuMat()
                    gpu_img.upload(img)
                    
                    # GPU上进行对比度增强
                    gpu_img_float = cv2.cuda.convertTo(gpu_img, cv2.CV_32F)
                    gpu_img_float = cv2.cuda.multiply(gpu_img_float, 1.5)
                    enhanced_img = cv2.cuda.convertTo(gpu_img_float, cv2.CV_8U)
                    
                    # 转换为灰度图
                    gpu_gray = cv2.cuda.cvtColor(enhanced_img, cv2.COLOR_RGB2GRAY)
                    
                    # 下载到CPU进行后续处理
                    gray = gpu_gray.download()
                    print("使用GPU加速处理图像")
                except Exception as e:
                    print(f"GPU处理失败，回退到CPU: {e}")
                    # 回退到CPU处理
                    img = cv2.convertScaleAbs(img, alpha=1.5, beta=0)
                    gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
            else:
                # CPU处理
                img = cv2.convertScaleAbs(img, alpha=1.5, beta=0)
                gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
            
            # 自适应二值化处理，更好地适应不同亮度条件
            thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                          cv2.THRESH_BINARY, 11, 2)
            
            # 使用pytesseract识别文本，增加中文识别支持
            text = pytesseract.image_to_string(thresh, lang='chi_sim+eng', 
                                              config='--psm 6')
            
            print(f"识别到的文本: {text}")  # 调试用
            
            # 匹配"XX分XX秒后解锁购买"格式
            match = re.search(r'(\d+)\s*分\s*(\d+)\s*秒', text)
            if match:
                minutes = int(match.group(1))
                seconds = int(match.group(2))
                total_seconds = minutes * 60 + seconds
                print(f"成功识别倒计时: {minutes}分{seconds}秒，共{total_seconds}秒")
                return total_seconds
                
            # 备用匹配格式 (数字:数字)
            match = re.search(r'(\d+)[:.]+(\d+)', text)
            if match:
                minutes = int(match.group(1))
                seconds = float(match.group(2))
                total_seconds = minutes * 60 + seconds
                print(f"使用备用格式识别倒计时: {minutes}:{seconds}，共{total_seconds}秒")
                return total_seconds
                
        except Exception as e:
            print(f"OCR识别失败: {e}，尝试使用备用方法...")
        
        # 备用方法：分析图像像素变化来估计倒计时
        return estimate_time_from_image(screenshot)
        
    except Exception as e:
        print(f"提取倒计时失败: {e}")
        return None

def estimate_time_from_image(img):
    """
    备用方法：通过图像分析估计倒计时
    专门针对"0分25秒后解锁购买"格式进行优化
    支持GPU加速处理
    """
    try:
        # 将PIL图像转换为OpenCV格式
        img_cv = np.array(img)
        
        # GPU加速图像处理
        if USE_GPU:
            try:
                # 上传图像到GPU
                gpu_img = cv2.cuda_GpuMat()
                gpu_img.upload(img_cv)
                
                # GPU上增强对比度
                gpu_img_float = cv2.cuda.convertTo(gpu_img, cv2.CV_32F)
                gpu_img_float = cv2.cuda.multiply(gpu_img_float, 2.0)
                gpu_img_float = cv2.cuda.add(gpu_img_float, 10.0)
                enhanced_img = cv2.cuda.convertTo(gpu_img_float, cv2.CV_8U)
                
                # 转为灰度图
                gpu_gray = cv2.cuda.cvtColor(enhanced_img, cv2.COLOR_RGB2GRAY)
                
                # 应用高斯模糊
                gpu_blurred = cv2.cuda.createGaussianFilter(cv2.CV_8UC1, cv2.CV_8UC1, (5, 5), 0)
                gpu_blurred = gpu_blurred.apply(gpu_gray)
                
                # 下载到CPU进行后续处理
                blurred = gpu_blurred.download()
                
                # 边缘检测和膨胀在CPU上进行，因为CUDA版本可能不完全支持
                edges = cv2.Canny(blurred, 50, 150)
                kernel = np.ones((2, 2), np.uint8)
                dilated = cv2.dilate(edges, kernel, iterations=1)
                
                print("使用GPU加速处理备用图像分析")
            except Exception as e:
                print(f"GPU处理备用图像分析失败，回退到CPU: {e}")
                # 回退到CPU处理
                img_cv = cv2.convertScaleAbs(img_cv, alpha=2.0, beta=10)
                gray = cv2.cvtColor(img_cv, cv2.COLOR_RGB2GRAY)
                blurred = cv2.GaussianBlur(gray, (5, 5), 0)
                edges = cv2.Canny(blurred, 50, 150)
                kernel = np.ones((2, 2), np.uint8)
                dilated = cv2.dilate(edges, kernel, iterations=1)
        else:
            # CPU处理
            img_cv = cv2.convertScaleAbs(img_cv, alpha=2.0, beta=10)
            gray = cv2.cvtColor(img_cv, cv2.COLOR_RGB2GRAY)
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            edges = cv2.Canny(blurred, 50, 150)
            kernel = np.ones((2, 2), np.uint8)
            dilated = cv2.dilate(edges, kernel, iterations=1)
        
        # 使用更宽松的配置进行OCR识别
        text = pytesseract.image_to_string(dilated, lang='chi_sim+eng', 
                                          config='--psm 11 --oem 3')
        
        print(f"备用方法识别文本: {text}")
        
        # 尝试匹配"X分X秒"格式
        match = re.search(r'(\d+)\s*分\s*(\d+)\s*秒', text)
        if match:
            minutes = int(match.group(1))
            seconds = int(match.group(2))
            total_seconds = minutes * 60 + seconds
            print(f"备用方法成功识别: {minutes}分{seconds}秒，共{total_seconds}秒")
            return total_seconds
            
        # 尝试直接提取数字
        numbers = re.findall(r'\d+', text)
        if len(numbers) >= 2:
            # 假设第一个数字是分钟，第二个是秒钟
            try:
                minutes = int(numbers[0])
                seconds = int(numbers[1])
                if minutes < 60 and seconds < 60:  # 合理性检查
                    total_seconds = minutes * 60 + seconds
                    print(f"通过数字提取识别: {minutes}分{seconds}秒，共{total_seconds}秒")
                    return total_seconds
            except:
                pass
                
        # 如果识别到"0分25秒"这种特定格式
        if "0分25秒" in text or "O分25秒" in text:  # OCR可能会把0识别为O
            print("检测到特定格式: 0分25秒")
            return 25.0
            
        # 如果识别到"25秒"
        sec_match = re.search(r'(\d+)\s*秒', text)
        if sec_match:
            seconds = int(sec_match.group(1))
            print(f"仅识别到秒数: {seconds}秒")
            return seconds
            
    except Exception as e:
        print(f"备用图像分析失败: {e}")
    
    # 默认返回一个安全值
    print("无法识别倒计时，使用默认值")
    return 5.0  # 默认5秒，确保有足够时间准备

def main():
    """
    主执行函数 - 优化版
    支持倒计时识别和精准抢购
    """
    # 1. 启动延迟
    print("请立即切换到游戏窗口！")
    print("\n脚本已启动，正在监测倒计时...")

    # 2. 监测倒计时并预测开抢时间
    remaining_time = None
    last_check_time = time.time()
    
    while True:
        try:
            # 检测倒计时区域
            current_time = extract_timer_value(TIMER_REGION)
            
            if current_time is not None:
                remaining_time = current_time
                print(f"检测到倒计时: {remaining_time:.2f}秒", end='\r')
                
                # 当倒计时接近结束时（小于PRE_CLICK_TIME秒）开始准备点击
                if remaining_time <= PRE_CLICK_TIME:
                    print(f"\n倒计时即将结束，还剩{remaining_time:.2f}秒！准备开始点击...")
                    break
            else:
                # 如果无法识别倒计时，使用备用方法
                if pyautogui.locateOnScreen(TIMER_SIGNAL_IMG, confidence=0.7, region=TIMER_REGION,
                                           grayscale=True) is not None:
                    print("\n检测到开始信号！准备抢购...")
                    remaining_time = 0.5  # 默认设置为0.5秒
                    break
            
            # 控制检测频率，避免过度消耗CPU
            elapsed = time.time() - last_check_time
            if elapsed < 0.1:  # 每0.1秒检测一次
                time.sleep(0.1 - elapsed)
            last_check_time = time.time()
            
        except Exception as e:
            print(f"监测倒计时出错: {e}，继续尝试...")
            time.sleep(0.1)

    # 3. 预先定位购买按钮的精确坐标
    purchase_button_center = None
    try:
        location = pyautogui.locateOnScreen(BUY_BUTTON_IMG, confidence=0.8, region=BUY_BUTTON_REGION, grayscale=True)
        if location:
            purchase_button_center = pyautogui.center(location)
            print(f"已锁定购买按钮坐标: {purchase_button_center}")
        else:
            print("警告：未直接定位到购买按钮图片，将使用购买区域的中心点进行点击。")
            purchase_button_center = (
                BUY_BUTTON_REGION[0] + BUY_BUTTON_REGION[2] // 2,
                BUY_BUTTON_REGION[1] + BUY_BUTTON_REGION[3] // 2
            )
    except Exception as e:
        print(f"定位初始购买按钮时出错: {e}。脚本将无法继续。")
        return

    # 4. 精确计算开始点击的时间
    if remaining_time > 0:
        wait_time = max(0, remaining_time - PRE_CLICK_TIME)
        if wait_time > 0:
            print(f"等待 {wait_time:.2f} 秒后开始点击...")
            time.sleep(wait_time)
    
    # 5. 进入高速点击模式
    print("进入高速点击模式...")
    start_time = time.time()
    click_end_time = start_time + PRE_CLICK_TIME + POST_CLICK_TIME
    confirm_detected = False
    
    # 高频点击直到检测到确认按钮或超时
    while time.time() < click_end_time and not confirm_detected:
        # 点击购买按钮
        pyautogui.click(purchase_button_center)
        
        # 检查是否出现确认按钮
        try:
            if pyautogui.locateOnScreen(CONFIRM_BUTTON_IMG, confidence=0.4,
                                      region=CONFIRM_BUTTON_REGION, grayscale=True) is not None:
                print("检测到确认购买窗口！立即切换目标！")
                confirm_detected = True
                break
        except pyautogui.ImageNotFoundException:
            pass
            
        time.sleep(CLICK_INTERVAL)
    
    # 6. 点击固定坐标的确认按钮
    print("正在点击确认购买按钮...")
    
    # 使用固定坐标点击确认按钮
    print(f"使用固定坐标 {CONFIRM_BUTTON_FIXED_POS} 点击确认按钮，连续点击 {CONFIRM_CLICKS} 次！")
    for i in range(CONFIRM_CLICKS):
        pyautogui.click(CONFIRM_BUTTON_FIXED_POS)
        time.sleep(CONFIRM_CLICK_INTERVAL)

    print("抢购成功！脚本执行完毕。")


if __name__ == '__main__':
    main()