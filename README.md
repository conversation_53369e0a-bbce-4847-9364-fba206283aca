# 三角洲抢砖皮 - 高性能抢购脚本

一个基于Python的高性能抢购脚本，支持倒计时识别、精准时间控制和GPU加速，专为三角洲行动曼德尔砖皮限时抢购设计。

## 功能特点

- **倒计时智能识别**：使用OCR技术自动识别"XX分XX秒后解锁购买"格式的倒计时
- **多重识别保障**：主方法失败时自动切换到备用图像分析方法
- **精准抢购时机**：在倒计时结束前自动开始点击，提高抢购成功率
- **高频点击模式**：在关键时间段内实现高频点击，突破普通人工点击速度限制
- **固定坐标确认**：支持对确认购买按钮进行固定坐标多次点击
- **GPU加速支持**：利用CUDA加速图像处理，提高识别速度和准确率

## 系统要求

- Python 3.6+
- Windows操作系统 (其他系统可能需要调整)
- CUDA支持的NVIDIA显卡 (可选，用于GPU加速)

## 安装依赖

```bash
pip install pyautogui opencv-python numpy pytesseract pillow
```

### Tesseract OCR 安装

1. 从[GitHub](https://github.com/UB-Mannheim/tesseract/wiki)下载并安装Tesseract OCR
2. 安装中文语言包
3. 在配置区域更新`PYTESSERACT_PATH`为你的安装路径

## 使用方法

### 1. 配置脚本

编辑`auto_buy.py`文件中的配置区域：

```python
# --- 请在这里配置你的搜索区域 (x, y, 宽度, 高度) ---
TIMER_REGION = (2178, 1216, 179, 27)  # 倒计时区域
BUY_BUTTON_REGION = (2084, 1160, 396, 195)  # 购买按钮区域
CONFIRM_BUTTON_REGION = (1253, 755, 540, 219)  # 确认按钮区域

# --- 性能配置 ---
CLICK_INTERVAL = 0.01  # 点击间隔
CONFIRM_CLICKS = 20  # 确认点击次数
CONFIRM_CLICK_INTERVAL = 0.01  # 确认点击间隔

# --- 新增配置 ---
CONFIRM_BUTTON_FIXED_POS = (1486, 888)  # 确认购买按钮的固定坐标
PRE_CLICK_TIME = 1.0  # 倒计时结束前多久开始点击（秒）
POST_CLICK_TIME = 0.1  # 倒计时结束后持续点击多久（秒）

# --- GPU加速配置 ---
USE_GPU = True  # 设置为False可禁用GPU加速
GPU_DEVICE_ID = 0  # 如果有多个GPU，可以选择使用哪一个
```

### 2. 获取坐标

使用提供的`get_coords.py`工具获取屏幕坐标：

```bash
python get_coords.py
```

将鼠标移动到需要点击的位置，记录显示的坐标。

### 3. 运行脚本

```bash
python auto_buy.py
```

运行后，脚本将自动监测倒计时区域，并在适当时机执行抢购操作。

## 注意事项

- 使用前请确保已正确配置所有区域坐标
- 脚本使用`pyautogui.FAILSAFE = True`，将鼠标快速移动到屏幕左上角可紧急停止脚本
- 请合理使用，避免对电商平台造成过大压力
- 不同分辨率的屏幕需要重新配置坐标

## 自定义开发

如需自定义开发，可以修改以下关键函数：

- `extract_timer_value`: 倒计时识别主函数
- `estimate_time_from_image`: 备用倒计时识别方法
- `main`: 主执行函数，控制抢购流程

## 许可证

MIT License

## 免责声明

本项目仅供学习和研究使用，请勿用于商业用途或违反相关平台规定的活动。使用本脚本造成的任何后果由使用者自行承担。